/**
 * Theme Context for Disc Golf Inventory Management System
 *
 * Provides theme management functionality with support for light, dark, and system themes.
 * Integrates with localStorage for persistence and applies theme changes immediately.
 */

"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useLocalStorage } from "@/hooks/useLocalStorage";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Available theme options
 */
export type Theme = "light" | "dark" | "system";

/**
 * Resolved theme (system resolves to light or dark)
 */
export type ResolvedTheme = "light" | "dark";

/**
 * Theme context interface
 */
interface ThemeContextType {
  /** Current theme setting */
  theme: Theme;
  /** Resolved theme (system preference resolved) */
  resolvedTheme: ResolvedTheme;
  /** Set the theme */
  setTheme: (theme: Theme) => void;
  /** Whether the theme is loading */
  loading: boolean;
}

// ============================================================================
// CONTEXT CREATION
// ============================================================================

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// ============================================================================
// THEME PROVIDER COMPONENT
// ============================================================================

/**
 * Theme provider props
 */
interface ThemeProviderProps {
  children: React.ReactNode;
  /** Default theme if none is stored */
  defaultTheme?: Theme;
  /** Storage key for theme persistence */
  storageKey?: string;
}

/**
 * Theme provider component
 *
 * Manages theme state, persistence, and application to the DOM.
 * Supports system theme detection and automatic updates.
 */
export function ThemeProvider({ children, defaultTheme = "system", storageKey = "app-theme" }: ThemeProviderProps) {
  const [mounted, setMounted] = useState(false);
  const [systemTheme, setSystemTheme] = useState<ResolvedTheme>("light");

  // Persist theme in localStorage
  const {
    value: theme,
    setValue: setStoredTheme,
    loading,
  } = useLocalStorage<Theme>(storageKey, { defaultValue: defaultTheme });

  /**
   * Get the resolved theme (convert system to actual theme)
   */
  const resolvedTheme: ResolvedTheme = theme === "system" ? systemTheme : theme || "light";

  /**
   * Set theme and apply to DOM
   */
  const setTheme = (newTheme: Theme) => {
    setStoredTheme(newTheme);
  };

  /**
   * Apply theme to DOM
   */
  const applyTheme = (resolvedTheme: ResolvedTheme) => {
    const root = document.documentElement;

    // Remove existing theme classes
    root.classList.remove("light", "dark");

    // Add new theme class
    root.classList.add(resolvedTheme);

    // Set data attribute for CSS selectors
    root.setAttribute("data-theme", resolvedTheme);
  };

  /**
   * Detect system theme preference
   */
  const detectSystemTheme = (): ResolvedTheme => {
    if (typeof window === "undefined") return "light";

    return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
  };

  /**
   * Handle system theme changes
   */
  useEffect(() => {
    if (typeof window === "undefined") return;

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");

    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? "dark" : "light");
    };

    // Set initial system theme
    setSystemTheme(detectSystemTheme());

    // Listen for system theme changes
    mediaQuery.addEventListener("change", handleChange);

    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  /**
   * Apply theme when resolved theme changes
   */
  useEffect(() => {
    if (mounted && !loading) {
      applyTheme(resolvedTheme);
    }
  }, [resolvedTheme, mounted, loading]);

  /**
   * Set mounted state after hydration
   */
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <ThemeContext.Provider
      value={{
        theme: theme || "system",
        resolvedTheme,
        setTheme,
        loading,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
}

// ============================================================================
// THEME HOOK
// ============================================================================

/**
 * Hook to access theme context
 *
 * @returns Theme context value
 * @throws Error if used outside ThemeProvider
 */
export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext);

  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }

  return context;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get theme display name for UI
 */
export function getThemeDisplayName(theme: Theme): string {
  switch (theme) {
    case "light":
      return "Light";
    case "dark":
      return "Dark";
    case "system":
      return "System";
    default:
      return "Unknown";
  }
}

/**
 * Get all available themes
 */
export function getAvailableThemes(): Theme[] {
  return ["light", "dark", "system"];
}
