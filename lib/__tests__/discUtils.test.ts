/**
 * Tests for Disc Utilities - Bag Analysis and Collection Health
 *
 * Comprehensive test suite for the new bag analysis and collection health
 * utility functions added to discUtils.ts.
 */

import { describe, it, expect } from "vitest";
import {
  classifyDiscType,
  classifyStability,
  analyzeBagComposition,
  generateBagRecommendations,
  calculateCollectionHealth,
  DiscType,
  StabilityType,
} from "../discUtils";
import type { Disc } from "../types";
import { DiscCondition, Location } from "../types";

// ============================================================================
// TEST DATA HELPERS
// ============================================================================

const createTestDisc = (overrides: Partial<Disc> = {}): Disc => ({
  id: "test-id",
  manufacturer: "Test Manufacturer",
  mold: "Test Mold",
  plasticType: "Test Plastic",
  weight: 175,
  condition: DiscCondition.GOOD,
  flightNumbers: { speed: 5, glide: 5, turn: 0, fade: 2 },
  color: "Blue",
  currentLocation: Location.HOME,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

// ============================================================================
// DISC CLASSIFICATION TESTS
// ============================================================================

describe("classifyDiscType", () => {
  it("should classify putters correctly", () => {
    expect(classifyDiscType(1)).toBe(DiscType.PUTTER);
    expect(classifyDiscType(2)).toBe(DiscType.PUTTER);
    expect(classifyDiscType(3)).toBe(DiscType.PUTTER);
  });

  it("should classify midranges correctly", () => {
    expect(classifyDiscType(4)).toBe(DiscType.MIDRANGE);
    expect(classifyDiscType(5)).toBe(DiscType.MIDRANGE);
    expect(classifyDiscType(6)).toBe(DiscType.MIDRANGE);
  });

  it("should classify fairway drivers correctly", () => {
    expect(classifyDiscType(7)).toBe(DiscType.FAIRWAY_DRIVER);
    expect(classifyDiscType(8)).toBe(DiscType.FAIRWAY_DRIVER);
    expect(classifyDiscType(9)).toBe(DiscType.FAIRWAY_DRIVER);
  });

  it("should classify distance drivers correctly", () => {
    expect(classifyDiscType(10)).toBe(DiscType.DISTANCE_DRIVER);
    expect(classifyDiscType(12)).toBe(DiscType.DISTANCE_DRIVER);
    expect(classifyDiscType(14)).toBe(DiscType.DISTANCE_DRIVER);
  });
});

describe("classifyStability", () => {
  it("should classify overstable discs correctly", () => {
    expect(classifyStability(-1, 3)).toBe(StabilityType.OVERSTABLE); // stability score = 4
    expect(classifyStability(0, 3)).toBe(StabilityType.OVERSTABLE); // stability score = 3
  });

  it("should classify stable discs correctly", () => {
    expect(classifyStability(-1, 1)).toBe(StabilityType.STABLE); // stability score = 2
    expect(classifyStability(0, 2)).toBe(StabilityType.STABLE); // stability score = 2
  });

  it("should classify understable discs correctly", () => {
    expect(classifyStability(-1, 0)).toBe(StabilityType.UNDERSTABLE); // stability score = 1
    expect(classifyStability(-2, 0)).toBe(StabilityType.STABLE); // stability score = 2
    expect(classifyStability(-3, 1)).toBe(StabilityType.OVERSTABLE); // stability score = 4
  });
});

// ============================================================================
// BAG ANALYSIS TESTS
// ============================================================================

describe("analyzeBagComposition", () => {
  it("should return empty composition for no discs", () => {
    const result = analyzeBagComposition([]);

    expect(result.totalDiscs).toBe(0);
    expect(result.byType[DiscType.PUTTER]).toBe(0);
    expect(result.recommendations).toEqual([]);
  });

  it("should return empty composition for no bag discs", () => {
    const discs = [
      createTestDisc({ currentLocation: Location.HOME }),
      createTestDisc({ currentLocation: Location.CAR }),
    ];

    const result = analyzeBagComposition(discs);

    expect(result.totalDiscs).toBe(0);
    expect(result.recommendations).toEqual([]);
  });

  it("should analyze bag composition correctly", () => {
    const discs = [
      createTestDisc({
        currentLocation: Location.BAG,
        flightNumbers: { speed: 2, glide: 3, turn: 0, fade: 1 },
      }),
      createTestDisc({
        currentLocation: Location.BAG,
        flightNumbers: { speed: 5, glide: 5, turn: 0, fade: 2 },
      }),
      createTestDisc({
        currentLocation: Location.BAG,
        flightNumbers: { speed: 9, glide: 5, turn: -1, fade: 2 },
      }),
      createTestDisc({ currentLocation: Location.HOME }), // Not in bag
    ];

    const result = analyzeBagComposition(discs);

    expect(result.totalDiscs).toBe(3);
    expect(result.byType[DiscType.PUTTER]).toBe(1);
    expect(result.byType[DiscType.MIDRANGE]).toBe(1);
    expect(result.byType[DiscType.FAIRWAY_DRIVER]).toBe(1);
    expect(result.byType[DiscType.DISTANCE_DRIVER]).toBe(0);
  });

  it("should calculate speed coverage correctly", () => {
    const discs = [
      createTestDisc({
        currentLocation: Location.BAG,
        flightNumbers: { speed: 2, glide: 3, turn: 0, fade: 1 },
      }),
      createTestDisc({
        currentLocation: Location.BAG,
        flightNumbers: { speed: 5, glide: 5, turn: 0, fade: 2 },
      }),
      createTestDisc({
        currentLocation: Location.BAG,
        flightNumbers: { speed: 9, glide: 5, turn: -1, fade: 2 },
      }),
    ];

    const result = analyzeBagComposition(discs);

    expect(result.speedCoverage.min).toBe(2);
    expect(result.speedCoverage.max).toBe(9);
    expect(result.speedCoverage.gaps).toEqual([3, 4, 6, 7, 8]);
  });
});

describe("generateBagRecommendations", () => {
  it("should recommend missing disc types", () => {
    const byType = {
      [DiscType.PUTTER]: 0,
      [DiscType.MIDRANGE]: 1,
      [DiscType.FAIRWAY_DRIVER]: 1,
      [DiscType.DISTANCE_DRIVER]: 1,
    };

    const byStability = {
      [StabilityType.OVERSTABLE]: 1,
      [StabilityType.STABLE]: 1,
      [StabilityType.UNDERSTABLE]: 1,
    };

    const speedCoverage = { min: 5, max: 12, gaps: [] };

    const recommendations = generateBagRecommendations(byType, byStability, speedCoverage);

    expect(recommendations).toHaveLength(1);
    expect(recommendations[0].type).toBe(DiscType.PUTTER);
    expect(recommendations[0].priority).toBe("high");
  });

  it("should recommend missing stability types", () => {
    const byType = {
      [DiscType.PUTTER]: 1,
      [DiscType.MIDRANGE]: 1,
      [DiscType.FAIRWAY_DRIVER]: 1,
      [DiscType.DISTANCE_DRIVER]: 1,
    };

    const byStability = {
      [StabilityType.OVERSTABLE]: 0,
      [StabilityType.STABLE]: 1,
      [StabilityType.UNDERSTABLE]: 0,
    };

    const speedCoverage = { min: 2, max: 12, gaps: [] };

    const recommendations = generateBagRecommendations(byType, byStability, speedCoverage);

    expect(recommendations.length).toBeGreaterThan(0);
    expect(recommendations.some((r) => r.stability === StabilityType.OVERSTABLE)).toBe(true);
    expect(recommendations.some((r) => r.stability === StabilityType.UNDERSTABLE)).toBe(true);
  });

  it("should sort recommendations by priority", () => {
    const byType = {
      [DiscType.PUTTER]: 0,
      [DiscType.MIDRANGE]: 0,
      [DiscType.FAIRWAY_DRIVER]: 1,
      [DiscType.DISTANCE_DRIVER]: 1,
    };

    const byStability = {
      [StabilityType.OVERSTABLE]: 0,
      [StabilityType.STABLE]: 1,
      [StabilityType.UNDERSTABLE]: 0,
    };

    const speedCoverage = { min: 7, max: 12, gaps: [8, 10] };

    const recommendations = generateBagRecommendations(byType, byStability, speedCoverage);

    // High priority items should come first
    const highPriorityCount = recommendations.filter((r) => r.priority === "high").length;
    const firstHighPriorityIndex = recommendations.findIndex((r) => r.priority === "high");
    const lastHighPriorityIndex = recommendations.findLastIndex((r) => r.priority === "high");

    if (highPriorityCount > 0) {
      expect(firstHighPriorityIndex).toBe(0);
      expect(lastHighPriorityIndex).toBe(highPriorityCount - 1);
    }
  });
});

// ============================================================================
// COLLECTION HEALTH TESTS
// ============================================================================

describe("calculateCollectionHealth", () => {
  it("should return empty health for no discs", () => {
    const result = calculateCollectionHealth([]);

    expect(result.overallScore).toBe(0);
    expect(result.replacementNeeded).toEqual([]);
    expect(result.valueAtRisk).toBe(0);
    expect(result.diversityScore).toBe(0);
    expect(result.usageEfficiency).toBe(0);
  });

  it("should calculate condition distribution correctly", () => {
    const discs = [
      createTestDisc({ condition: DiscCondition.NEW }),
      createTestDisc({ condition: DiscCondition.GOOD }),
      createTestDisc({ condition: DiscCondition.WORN }),
      createTestDisc({ condition: DiscCondition.DAMAGED }),
    ];

    const result = calculateCollectionHealth(discs);

    expect(result.conditionDistribution[DiscCondition.NEW]).toBe(1);
    expect(result.conditionDistribution[DiscCondition.GOOD]).toBe(1);
    expect(result.conditionDistribution[DiscCondition.WORN]).toBe(1);
    expect(result.conditionDistribution[DiscCondition.DAMAGED]).toBe(1);
    expect(result.conditionDistribution[DiscCondition.FAIR]).toBe(0);
  });

  it("should identify discs needing replacement", () => {
    const discs = [
      createTestDisc({ condition: DiscCondition.NEW }),
      createTestDisc({ condition: DiscCondition.WORN, purchasePrice: 20 }),
      createTestDisc({ condition: DiscCondition.DAMAGED, purchasePrice: 15 }),
    ];

    const result = calculateCollectionHealth(discs);

    expect(result.replacementNeeded).toHaveLength(2);
    expect(result.valueAtRisk).toBe(35);
  });

  it("should calculate diversity score", () => {
    const discs = [
      createTestDisc({ manufacturer: "Innova", mold: "Destroyer", plasticType: "Champion" }),
      createTestDisc({ manufacturer: "Discraft", mold: "Buzzz", plasticType: "ESP" }),
      createTestDisc({ manufacturer: "Dynamic", mold: "Judge", plasticType: "Lucid" }),
    ];

    const result = calculateCollectionHealth(discs);

    // 3 manufacturers * 10 + 3 molds * 2 + 3 plastics = 39
    expect(result.diversityScore).toBe(39);
  });

  it("should calculate usage efficiency", () => {
    const discs = [
      createTestDisc({ currentLocation: Location.BAG }),
      createTestDisc({ currentLocation: Location.BAG }),
      createTestDisc({ currentLocation: Location.HOME }),
      createTestDisc({ currentLocation: Location.CAR }),
    ];

    const result = calculateCollectionHealth(discs);

    // 2 out of 4 discs in bag = 50%
    expect(result.usageEfficiency).toBe(50);
  });

  it("should calculate overall health score", () => {
    const discs = [
      createTestDisc({
        condition: DiscCondition.NEW,
        manufacturer: "Innova",
        mold: "Destroyer",
        plasticType: "Champion",
        currentLocation: Location.BAG,
      }),
      createTestDisc({
        condition: DiscCondition.GOOD,
        manufacturer: "Discraft",
        mold: "Buzzz",
        plasticType: "ESP",
        currentLocation: Location.BAG,
      }),
    ];

    const result = calculateCollectionHealth(discs);

    expect(result.overallScore).toBeGreaterThan(0);
    expect(result.overallScore).toBeLessThanOrEqual(100);
  });
});
