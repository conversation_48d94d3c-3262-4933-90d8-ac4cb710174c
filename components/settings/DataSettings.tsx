/**
 * Data Settings Component for Disc Golf Inventory Management System
 *
 * Manages data backup, storage preferences, and data management options.
 */

"use client";

import * as React from "react";
import { SettingsCard, SettingsItem, SettingsGroup } from "./SettingsCard";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { AppSettingsType } from "@/lib/validation";
import { Database, Download, Upload, Trash2, AlertTriangle, HardDrive } from "lucide-react";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Data settings props
 */
interface DataSettingsProps {
  /** Current data settings */
  settings: AppSettingsType["data"];
  /** Callback to update settings */
  onUpdate: (updates: Partial<AppSettingsType["data"]>) => void;
  /** Callback to reset all settings */
  onReset: () => void;
}

// ============================================================================
// DATA SETTINGS COMPONENT
// ============================================================================

/**
 * Data settings component
 *
 * Provides controls for backup preferences, storage management,
 * and data import/export functionality.
 */
export function DataSettings({ settings, onUpdate, onReset }: DataSettingsProps) {
  const [showResetConfirm, setShowResetConfirm] = React.useState(false);

  /**
   * Handle auto backup toggle
   */
  const handleAutoBackupChange = (enabled: boolean) => {
    onUpdate({ autoBackup: enabled });
  };

  /**
   * Handle backup frequency change
   */
  const handleBackupFrequencyChange = (frequency: unknown) => {
    const frequencyValue = frequency as AppSettingsType["data"]["backupFrequency"];
    onUpdate({ backupFrequency: frequencyValue });
  };

  /**
   * Handle max storage size change
   */
  const handleMaxStorageSizeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value, 10);
    if (!isNaN(value) && value >= 1 && value <= 100) {
      onUpdate({ maxStorageSize: value });
    }
  };

  /**
   * Handle compression toggle
   */
  const handleCompressionChange = (enabled: boolean) => {
    onUpdate({ compressionEnabled: enabled });
  };

  /**
   * Handle data export
   */
  const handleExportData = () => {
    // TODO: Implement data export functionality
    console.log("Exporting data...");
  };

  /**
   * Handle data import
   */
  const handleImportData = () => {
    // TODO: Implement data import functionality
    console.log("Importing data...");
  };

  /**
   * Handle reset confirmation
   */
  const handleResetConfirm = () => {
    onReset();
    setShowResetConfirm(false);
  };

  return (
    <div className="space-y-6">
      {/* Backup Settings */}
      <SettingsCard
        title="Backup & Sync"
        description="Manage automatic backups and data synchronization"
        icon={Database}
      >
        <SettingsGroup title="Automatic Backup" description="Automatically create backups of your collection data">
          <SettingsItem
            label="Enable auto backup"
            description="Automatically backup your data to browser storage"
            control={
              <Switch
                checked={settings.autoBackup}
                onCheckedChange={handleAutoBackupChange}
                aria-label="Toggle auto backup"
              />
            }
          />

          <SettingsItem
            label="Backup frequency"
            description="How often to create automatic backups"
            disabled={!settings.autoBackup}
            control={
              <Select
                value={settings.backupFrequency}
                onValueChange={handleBackupFrequencyChange}
                disabled={!settings.autoBackup}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            }
          />
        </SettingsGroup>
      </SettingsCard>

      {/* Storage Settings */}
      <SettingsCard title="Storage Management" description="Control how data is stored and managed" icon={HardDrive}>
        <SettingsGroup title="Storage Options" description="Configure storage limits and optimization">
          <SettingsItem
            label="Max storage size"
            description="Maximum storage size in MB (1-100)"
            control={
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  min="1"
                  max="100"
                  value={settings.maxStorageSize}
                  onChange={handleMaxStorageSizeChange}
                  className="w-20"
                />
                <span className="text-sm text-muted-foreground">MB</span>
              </div>
            }
          />

          <SettingsItem
            label="Enable compression"
            description="Compress data to save storage space"
            control={
              <Switch
                checked={settings.compressionEnabled}
                onCheckedChange={handleCompressionChange}
                aria-label="Toggle data compression"
              />
            }
          />
        </SettingsGroup>
      </SettingsCard>

      {/* Data Management */}
      <SettingsCard
        title="Data Management"
        description="Import, export, and manage your collection data"
        icon={Download}
      >
        <SettingsGroup title="Import & Export" description="Transfer your data to and from the application">
          <div className="flex flex-col sm:flex-row gap-3">
            <Button variant="outline" onClick={handleExportData} className="flex items-center space-x-2">
              <Download className="h-4 w-4" />
              <span>Export Data</span>
            </Button>

            <Button variant="outline" onClick={handleImportData} className="flex items-center space-x-2">
              <Upload className="h-4 w-4" />
              <span>Import Data</span>
            </Button>
          </div>
        </SettingsGroup>

        <SettingsCard.Divider />

        <SettingsGroup title="Reset Data" description="Clear all data and reset to defaults">
          {!showResetConfirm ? (
            <Button
              variant="destructive"
              onClick={() => setShowResetConfirm(true)}
              className="flex items-center space-x-2"
            >
              <Trash2 className="h-4 w-4" />
              <span>Reset All Data</span>
            </Button>
          ) : (
            <div className="space-y-3">
              <div className="flex items-start space-x-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
                <AlertTriangle className="h-5 w-5 text-destructive mt-0.5" />
                <div className="space-y-1">
                  <div className="text-sm font-medium text-destructive">Are you sure?</div>
                  <div className="text-xs text-muted-foreground">
                    This will permanently delete all your discs, settings, and data. This action cannot be undone.
                  </div>
                </div>
              </div>
              <div className="flex space-x-2">
                <Button variant="destructive" size="sm" onClick={handleResetConfirm}>
                  Yes, Reset Everything
                </Button>
                <Button variant="outline" size="sm" onClick={() => setShowResetConfirm(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </SettingsGroup>
      </SettingsCard>
    </div>
  );
}
