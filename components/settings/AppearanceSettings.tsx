/**
 * Appearance Settings Component for Disc Golf Inventory Management System
 *
 * Manages theme selection, display preferences, and visual customization options.
 */

"use client";

import * as React from "react";
import { SettingsCard, SettingsItem, SettingsGroup } from "./SettingsCard";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useTheme, getThemeDisplayName, getAvailableThemes } from "@/contexts/ThemeContext";
import { AppSettingsType } from "@/lib/validation";
import { Palette, Monitor, Grid, List, Eye } from "lucide-react";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Appearance settings props
 */
interface AppearanceSettingsProps {
  /** Current appearance settings */
  settings: AppSettingsType["appearance"];
  /** Callback to update settings */
  onUpdate: (updates: Partial<AppSettingsType["appearance"]>) => void;
}

// ============================================================================
// APPEARANCE SETTINGS COMPONENT
// ============================================================================

/**
 * Appearance settings component
 *
 * Provides controls for theme selection, compact mode, flight numbers display,
 * and default view preferences.
 */
export function AppearanceSettings({ settings, onUpdate }: AppearanceSettingsProps) {
  const { theme, setTheme } = useTheme();

  /**
   * Handle theme change
   */
  const handleThemeChange = (newTheme: unknown) => {
    const themeValue = newTheme as AppSettingsType["appearance"]["theme"];
    setTheme(themeValue);
    onUpdate({ theme: themeValue });
  };

  /**
   * Handle compact mode toggle
   */
  const handleCompactModeChange = (enabled: boolean) => {
    onUpdate({ compactMode: enabled });
  };

  /**
   * Handle flight numbers display toggle
   */
  const handleFlightNumbersChange = (enabled: boolean) => {
    onUpdate({ showFlightNumbers: enabled });
  };

  /**
   * Handle default view change
   */
  const handleDefaultViewChange = (view: unknown) => {
    const viewValue = view as AppSettingsType["appearance"]["defaultView"];
    onUpdate({ defaultView: viewValue });
  };

  return (
    <div className="space-y-6">
      {/* Theme Settings */}
      <SettingsCard title="Theme" description="Choose your preferred color scheme" icon={Palette}>
        <SettingsItem
          label="Color scheme"
          description="Select light, dark, or system theme"
          control={
            <Select value={theme} onValueChange={handleThemeChange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {getAvailableThemes().map((themeOption) => (
                  <SelectItem key={themeOption} value={themeOption}>
                    <div className="flex items-center space-x-2">
                      {themeOption === "system" && <Monitor className="h-4 w-4" />}
                      {themeOption === "light" && (
                        <div className="h-4 w-4 rounded-full bg-white border border-gray-300" />
                      )}
                      {themeOption === "dark" && <div className="h-4 w-4 rounded-full bg-gray-900" />}
                      <span>{getThemeDisplayName(themeOption)}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          }
        />
      </SettingsCard>

      {/* Display Settings */}
      <SettingsCard title="Display" description="Customize how information is displayed" icon={Eye}>
        <SettingsGroup title="Layout Options" description="Control the density and layout of content">
          <SettingsItem
            label="Compact mode"
            description="Use a more condensed layout to show more content"
            control={
              <Switch
                checked={settings.compactMode}
                onCheckedChange={handleCompactModeChange}
                aria-label="Toggle compact mode"
              />
            }
          />

          <SettingsItem
            label="Default view"
            description="Choose how discs are displayed by default"
            control={
              <Select value={settings.defaultView} onValueChange={handleDefaultViewChange}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="grid">
                    <div className="flex items-center space-x-2">
                      <Grid className="h-4 w-4" />
                      <span>Grid</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="list">
                    <div className="flex items-center space-x-2">
                      <List className="h-4 w-4" />
                      <span>List</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            }
          />
        </SettingsGroup>

        <SettingsCard.Divider />

        <SettingsGroup title="Content Display" description="Control what information is shown">
          <SettingsItem
            label="Show flight numbers"
            description="Display flight numbers on disc cards and lists"
            control={
              <Switch
                checked={settings.showFlightNumbers}
                onCheckedChange={handleFlightNumbersChange}
                aria-label="Toggle flight numbers display"
              />
            }
          />
        </SettingsGroup>
      </SettingsCard>

      {/* Preview Section */}
      <SettingsCard title="Preview" description="See how your settings affect the interface">
        <div className="p-4 border border-border rounded-lg bg-muted/50">
          <div className="text-sm text-muted-foreground mb-2">Preview of your current settings:</div>
          <div className="space-y-2 text-xs">
            <div>
              Theme: <span className="font-medium">{getThemeDisplayName(theme)}</span>
            </div>
            <div>
              Layout: <span className="font-medium">{settings.compactMode ? "Compact" : "Comfortable"}</span>
            </div>
            <div>
              Default view: <span className="font-medium">{settings.defaultView === "grid" ? "Grid" : "List"}</span>
            </div>
            <div>
              Flight numbers: <span className="font-medium">{settings.showFlightNumbers ? "Shown" : "Hidden"}</span>
            </div>
          </div>
        </div>
      </SettingsCard>
    </div>
  );
}
