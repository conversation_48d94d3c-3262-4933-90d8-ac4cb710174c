/**
 * Advanced Settings Component for Disc Golf Inventory Management System
 *
 * Manages developer options, performance settings, and experimental features.
 */

"use client";

import * as React from "react";
import { SettingsCard, SettingsItem, SettingsGroup } from "./SettingsCard";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { AppSettingsType } from "@/lib/validation";
import { Zap, Bug, Gauge, FlaskRound, AlertTriangle, RotateCcw } from "lucide-react";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Advanced settings props
 */
interface AdvancedSettingsProps {
  /** Current advanced settings */
  settings: AppSettingsType["advanced"];
  /** Callback to update settings */
  onUpdate: (updates: Partial<AppSettingsType["advanced"]>) => void;
  /** Callback to reset all settings */
  onReset: () => void;
}

// ============================================================================
// ADVANCED SETTINGS COMPONENT
// ============================================================================

/**
 * Advanced settings component
 *
 * Provides controls for developer options, performance settings,
 * and experimental features.
 */
export function AdvancedSettings({ settings, onUpdate, onReset }: AdvancedSettingsProps) {
  /**
   * Handle debug mode toggle
   */
  const handleDebugModeChange = (enabled: boolean) => {
    onUpdate({ debugMode: enabled });

    // Apply debug mode immediately
    if (enabled) {
      console.log("Debug mode enabled");
      // Add debug styles or logging
    } else {
      console.log("Debug mode disabled");
    }
  };

  /**
   * Handle performance mode toggle
   */
  const handlePerformanceModeChange = (enabled: boolean) => {
    onUpdate({ performanceMode: enabled });

    // Apply performance optimizations immediately
    if (enabled) {
      console.log("Performance mode enabled");
      // Enable performance optimizations
    } else {
      console.log("Performance mode disabled");
    }
  };

  /**
   * Handle experimental features toggle
   */
  const handleExperimentalFeaturesChange = (enabled: boolean) => {
    onUpdate({ experimentalFeatures: enabled });

    if (enabled) {
      console.log("Experimental features enabled");
    } else {
      console.log("Experimental features disabled");
    }
  };

  /**
   * Clear browser cache and storage
   */
  const handleClearCache = () => {
    if ("caches" in window) {
      caches.keys().then((names) => {
        names.forEach((name) => {
          caches.delete(name);
        });
      });
    }

    // Clear session storage
    sessionStorage.clear();

    console.log("Cache cleared");
    alert("Cache cleared successfully. Please refresh the page.");
  };

  /**
   * Reset advanced settings only
   */
  const handleResetAdvanced = () => {
    onUpdate({
      debugMode: false,
      performanceMode: false,
      experimentalFeatures: false,
    });
  };

  return (
    <div className="space-y-6">
      {/* Developer Options */}
      <SettingsCard title="Developer Options" description="Tools and options for debugging and development" icon={Bug}>
        <SettingsGroup title="Debug Settings" description="Enable debugging features and verbose logging">
          <SettingsItem
            label="Debug mode"
            description="Enable console logging and debug information"
            control={
              <Switch
                checked={settings.debugMode}
                onCheckedChange={handleDebugModeChange}
                aria-label="Toggle debug mode"
              />
            }
          />
        </SettingsGroup>

        {settings.debugMode && (
          <div className="mt-4 p-3 bg-muted/50 border border-border rounded-md">
            <div className="text-xs text-muted-foreground space-y-1">
              <div>Debug mode is active</div>
              <div>Check browser console for debug information</div>
            </div>
          </div>
        )}
      </SettingsCard>

      {/* Performance Settings */}
      <SettingsCard title="Performance" description="Optimize performance and resource usage" icon={Gauge}>
        <SettingsGroup title="Performance Optimizations" description="Enable features to improve app performance">
          <SettingsItem
            label="Performance mode"
            description="Enable performance optimizations and reduce animations"
            control={
              <Switch
                checked={settings.performanceMode}
                onCheckedChange={handlePerformanceModeChange}
                aria-label="Toggle performance mode"
              />
            }
          />
        </SettingsGroup>

        <SettingsCard.Divider />

        <SettingsGroup title="Cache Management" description="Manage browser cache and temporary data">
          <Button variant="outline" onClick={handleClearCache} className="flex items-center space-x-2">
            <RotateCcw className="h-4 w-4" />
            <span>Clear Cache</span>
          </Button>
        </SettingsGroup>
      </SettingsCard>

      {/* Experimental Features */}
      <SettingsCard
        title="Experimental Features"
        description="Try out new features that are still in development"
        icon={FlaskRound}
      >
        <div className="flex items-start space-x-2 p-3 bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-md mb-4">
          <AlertTriangle className="h-5 w-5 text-orange-600 dark:text-orange-400 mt-0.5" />
          <div className="space-y-1">
            <div className="text-sm font-medium text-orange-800 dark:text-orange-200">
              Experimental Features Warning
            </div>
            <div className="text-xs text-orange-700 dark:text-orange-300">
              These features are experimental and may not work as expected. Use at your own risk.
            </div>
          </div>
        </div>

        <SettingsGroup title="Beta Features" description="Enable experimental functionality">
          <SettingsItem
            label="Experimental features"
            description="Enable access to experimental and beta features"
            control={
              <Switch
                checked={settings.experimentalFeatures}
                onCheckedChange={handleExperimentalFeaturesChange}
                aria-label="Toggle experimental features"
              />
            }
          />
        </SettingsGroup>

        {settings.experimentalFeatures && (
          <div className="mt-4 p-3 bg-muted/50 border border-border rounded-md">
            <div className="text-xs text-muted-foreground space-y-1">
              <div>Experimental features are enabled</div>
              <div>New experimental options may appear in other settings sections</div>
            </div>
          </div>
        )}
      </SettingsCard>

      {/* Reset Section */}
      <SettingsCard
        title="Reset Advanced Settings"
        description="Reset only the advanced settings to their defaults"
        icon={RotateCcw}
      >
        <SettingsGroup>
          <div className="flex flex-col space-y-3">
            <div className="text-sm text-muted-foreground">
              This will reset only the advanced settings (debug mode, performance mode, and experimental features) to
              their default values. Your appearance and data settings will not be affected.
            </div>
            <Button variant="outline" onClick={handleResetAdvanced} className="flex items-center space-x-2 w-fit">
              <RotateCcw className="h-4 w-4" />
              <span>Reset Advanced Settings</span>
            </Button>
          </div>
        </SettingsGroup>
      </SettingsCard>

      {/* System Information */}
      <SettingsCard title="System Information" description="Information about the application and browser" icon={Zap}>
        <div className="space-y-3 text-sm">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="font-medium">App Version</div>
              <div className="text-muted-foreground">1.0.0</div>
            </div>
            <div>
              <div className="font-medium">Build Date</div>
              <div className="text-muted-foreground">{new Date().toLocaleDateString()}</div>
            </div>
            <div>
              <div className="font-medium">Browser</div>
              <div className="text-muted-foreground">{navigator.userAgent.split(" ")[0]}</div>
            </div>
            <div>
              <div className="font-medium">Local Storage</div>
              <div className="text-muted-foreground">
                {typeof Storage !== "undefined" ? "Supported" : "Not supported"}
              </div>
            </div>
          </div>
        </div>
      </SettingsCard>
    </div>
  );
}
