/**
 * Settings Card Component for Disc Golf Inventory Management System
 * 
 * A reusable card component for individual settings sections.
 * Provides consistent layout and styling for settings groups.
 */

import * as React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Settings card props
 */
interface SettingsCardProps {
  /** Card title */
  title: string;
  /** Optional description */
  description?: string;
  /** Card content */
  children: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** Optional icon component */
  icon?: React.ComponentType<{ className?: string }>;
  /** Whether the card is disabled */
  disabled?: boolean;
}

/**
 * Settings item props for individual setting rows
 */
interface SettingsItemProps {
  /** Setting label */
  label: string;
  /** Optional description */
  description?: string;
  /** Setting control (switch, select, etc.) */
  control: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** Whether the item is disabled */
  disabled?: boolean;
}

/**
 * Settings group props for grouping related settings
 */
interface SettingsGroupProps {
  /** Group title */
  title?: string;
  /** Group description */
  description?: string;
  /** Group content */
  children: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
}

// ============================================================================
// SETTINGS CARD COMPONENT
// ============================================================================

/**
 * Settings card component
 * 
 * @example
 * ```tsx
 * <SettingsCard
 *   title="Appearance"
 *   description="Customize the look and feel"
 *   icon={Palette}
 * >
 *   <SettingsItem
 *     label="Dark mode"
 *     description="Use dark theme"
 *     control={<Switch checked={darkMode} onCheckedChange={setDarkMode} />}
 *   />
 * </SettingsCard>
 * ```
 */
export function SettingsCard({
  title,
  description,
  children,
  className,
  icon: Icon,
  disabled = false
}: SettingsCardProps) {
  return (
    <Card className={cn("w-full", disabled && "opacity-50", className)}>
      <CardHeader>
        <div className="flex items-center space-x-2">
          {Icon && <Icon className="h-5 w-5 text-muted-foreground" />}
          <CardTitle className="text-lg">{title}</CardTitle>
        </div>
        {description && (
          <CardDescription>{description}</CardDescription>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {children}
      </CardContent>
    </Card>
  );
}

// ============================================================================
// SETTINGS ITEM COMPONENT
// ============================================================================

/**
 * Individual settings item component
 * 
 * @example
 * ```tsx
 * <SettingsItem
 *   label="Enable notifications"
 *   description="Receive updates about your collection"
 *   control={
 *     <Switch 
 *       checked={notifications} 
 *       onCheckedChange={setNotifications} 
 *     />
 *   }
 * />
 * ```
 */
export function SettingsItem({
  label,
  description,
  control,
  className,
  disabled = false
}: SettingsItemProps) {
  return (
    <div className={cn(
      "flex items-center justify-between space-x-4 py-2",
      disabled && "opacity-50 pointer-events-none",
      className
    )}>
      <div className="flex-1 space-y-1">
        <div className="text-sm font-medium leading-none">
          {label}
        </div>
        {description && (
          <div className="text-xs text-muted-foreground">
            {description}
          </div>
        )}
      </div>
      <div className="flex-shrink-0">
        {control}
      </div>
    </div>
  );
}

// ============================================================================
// SETTINGS GROUP COMPONENT
// ============================================================================

/**
 * Settings group component for organizing related settings
 * 
 * @example
 * ```tsx
 * <SettingsGroup
 *   title="Display Options"
 *   description="Control how information is displayed"
 * >
 *   <SettingsItem ... />
 *   <SettingsItem ... />
 * </SettingsGroup>
 * ```
 */
export function SettingsGroup({
  title,
  description,
  children,
  className
}: SettingsGroupProps) {
  return (
    <div className={cn("space-y-3", className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h4 className="text-sm font-medium text-foreground">
              {title}
            </h4>
          )}
          {description && (
            <p className="text-xs text-muted-foreground">
              {description}
            </p>
          )}
        </div>
      )}
      <div className="space-y-2">
        {children}
      </div>
    </div>
  );
}

// ============================================================================
// SETTINGS DIVIDER COMPONENT
// ============================================================================

/**
 * Settings divider component
 */
export function SettingsDivider({ className }: { className?: string }) {
  return (
    <div className={cn("border-t border-border", className)} />
  );
}

// ============================================================================
// COMPOUND EXPORTS
// ============================================================================

/**
 * Compound component exports for easier usage
 */
SettingsCard.Item = SettingsItem;
SettingsCard.Group = SettingsGroup;
SettingsCard.Divider = SettingsDivider;
