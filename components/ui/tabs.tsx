/**
 * Tabs Component for Disc Golf Inventory Management System
 * 
 * A simple tabs implementation using buttons for navigation.
 * Built with accessibility support and consistent styling.
 */

"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Tab item interface
 */
interface TabItem {
  /** Unique identifier for the tab */
  id: string;
  /** Display label for the tab */
  label: string;
  /** Optional icon component */
  icon?: React.ComponentType<{ className?: string }>;
  /** Whether the tab is disabled */
  disabled?: boolean;
}

/**
 * Tabs root component props
 */
interface TabsProps {
  /** Array of tab items */
  tabs: TabItem[];
  /** Currently active tab ID */
  activeTab: string;
  /** Callback when tab changes */
  onTabChange: (tabId: string) => void;
  /** Additional CSS classes */
  className?: string;
  /** Tab variant */
  variant?: 'default' | 'pills' | 'underline';
  /** Tab size */
  size?: 'sm' | 'md' | 'lg';
  /** Whether tabs should fill available width */
  fullWidth?: boolean;
}

/**
 * Tab content component props
 */
interface TabContentProps {
  /** Tab ID this content belongs to */
  tabId: string;
  /** Currently active tab ID */
  activeTab: string;
  /** Content to render */
  children: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
}

// ============================================================================
// TABS COMPONENT
// ============================================================================

/**
 * Tabs navigation component
 * 
 * @example
 * ```tsx
 * const tabs = [
 *   { id: 'appearance', label: 'Appearance', icon: Palette },
 *   { id: 'data', label: 'Data', icon: Database },
 *   { id: 'advanced', label: 'Advanced', icon: Settings }
 * ];
 * 
 * <Tabs
 *   tabs={tabs}
 *   activeTab={activeTab}
 *   onTabChange={setActiveTab}
 * />
 * ```
 */
export function Tabs({
  tabs,
  activeTab,
  onTabChange,
  className,
  variant = 'default',
  size = 'md',
  fullWidth = false
}: TabsProps) {
  /**
   * Get variant-specific classes
   */
  const getVariantClasses = () => {
    switch (variant) {
      case 'pills':
        return {
          container: 'bg-muted p-1 rounded-lg',
          tab: 'rounded-md data-[active=true]:bg-background data-[active=true]:shadow-sm',
          activeIndicator: false
        };
      case 'underline':
        return {
          container: 'border-b border-border',
          tab: 'border-b-2 border-transparent data-[active=true]:border-primary rounded-none',
          activeIndicator: false
        };
      default:
        return {
          container: 'border-b border-border',
          tab: 'data-[active=true]:bg-accent data-[active=true]:text-accent-foreground',
          activeIndicator: false
        };
    }
  };

  /**
   * Get size-specific classes
   */
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-xs';
      case 'lg':
        return 'px-6 py-3 text-base';
      default:
        return 'px-4 py-2 text-sm';
    }
  };

  const variantClasses = getVariantClasses();
  const sizeClasses = getSizeClasses();

  /**
   * Handle tab click
   */
  const handleTabClick = (tabId: string, disabled?: boolean) => {
    if (!disabled) {
      onTabChange(tabId);
    }
  };

  /**
   * Handle keyboard navigation
   */
  const handleKeyDown = (event: React.KeyboardEvent, tabId: string, disabled?: boolean) => {
    if ((event.key === 'Enter' || event.key === ' ') && !disabled) {
      event.preventDefault();
      onTabChange(tabId);
    }
  };

  return (
    <div
      role="tablist"
      className={cn(
        "flex",
        fullWidth ? "w-full" : "w-fit",
        variantClasses.container,
        className
      )}
    >
      {tabs.map((tab) => {
        const isActive = tab.id === activeTab;
        const Icon = tab.icon;

        return (
          <button
            key={tab.id}
            role="tab"
            aria-selected={isActive}
            aria-controls={`tabpanel-${tab.id}`}
            id={`tab-${tab.id}`}
            data-active={isActive}
            disabled={tab.disabled}
            className={cn(
              // Base styles
              "inline-flex items-center justify-center whitespace-nowrap font-medium transition-colors",
              "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
              "disabled:pointer-events-none disabled:opacity-50",
              // Size classes
              sizeClasses,
              // Variant classes
              variantClasses.tab,
              // Full width
              fullWidth && "flex-1",
              // Default colors
              !isActive && "text-muted-foreground hover:text-foreground"
            )}
            onClick={() => handleTabClick(tab.id, tab.disabled)}
            onKeyDown={(e) => handleKeyDown(e, tab.id, tab.disabled)}
          >
            {Icon && (
              <Icon className={cn("shrink-0", size === 'sm' ? "h-3 w-3" : "h-4 w-4", tab.label && "mr-2")} />
            )}
            {tab.label}
          </button>
        );
      })}
    </div>
  );
}

// ============================================================================
// TAB CONTENT COMPONENT
// ============================================================================

/**
 * Tab content component
 * 
 * @example
 * ```tsx
 * <TabContent tabId="appearance" activeTab={activeTab}>
 *   <AppearanceSettings />
 * </TabContent>
 * ```
 */
export function TabContent({
  tabId,
  activeTab,
  children,
  className
}: TabContentProps) {
  const isActive = tabId === activeTab;

  if (!isActive) {
    return null;
  }

  return (
    <div
      role="tabpanel"
      id={`tabpanel-${tabId}`}
      aria-labelledby={`tab-${tabId}`}
      className={cn("mt-6", className)}
    >
      {children}
    </div>
  );
}

// ============================================================================
// COMPOUND COMPONENT
// ============================================================================

/**
 * Complete tabs component with content
 */
interface TabsWithContentProps extends Omit<TabsProps, 'children'> {
  /** Tab content mapping */
  content: Record<string, React.ReactNode>;
  /** Additional CSS classes for content area */
  contentClassName?: string;
}

/**
 * Tabs component with integrated content management
 * 
 * @example
 * ```tsx
 * <TabsWithContent
 *   tabs={tabs}
 *   activeTab={activeTab}
 *   onTabChange={setActiveTab}
 *   content={{
 *     appearance: <AppearanceSettings />,
 *     data: <DataSettings />,
 *     advanced: <AdvancedSettings />
 *   }}
 * />
 * ```
 */
export function TabsWithContent({
  content,
  contentClassName,
  ...tabsProps
}: TabsWithContentProps) {
  return (
    <div>
      <Tabs {...tabsProps} />
      <div className={cn("mt-6", contentClassName)}>
        {Object.entries(content).map(([tabId, tabContent]) => (
          <TabContent
            key={tabId}
            tabId={tabId}
            activeTab={tabsProps.activeTab}
          >
            {tabContent}
          </TabContent>
        ))}
      </div>
    </div>
  );
}
