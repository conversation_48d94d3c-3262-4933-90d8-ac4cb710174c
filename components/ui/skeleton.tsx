/**
 * Skeleton Component for Disc Golf Inventory Management System
 * 
 * A loading skeleton component for placeholder content while data is loading.
 */

import * as React from "react";
import { cn } from "@/lib/utils";

// ============================================================================
// SKELETON COMPONENT
// ============================================================================

/**
 * Skeleton component props
 */
interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Additional CSS classes */
  className?: string;
}

/**
 * Skeleton component for loading states
 * 
 * @example
 * ```tsx
 * <Skeleton className="h-4 w-32" />
 * <Skeleton className="h-20 w-full rounded-lg" />
 * ```
 */
function Skeleton({ className, ...props }: SkeletonProps) {
  return (
    <div
      className={cn(
        "animate-pulse rounded-md bg-muted",
        className
      )}
      {...props}
    />
  );
}

export { Skeleton };
