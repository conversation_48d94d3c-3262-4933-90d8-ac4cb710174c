/**
 * Switch Component for Disc Golf Inventory Management System
 * 
 * A toggle switch component for boolean settings with accessibility support.
 * Built using Base UI patterns and Tailwind CSS.
 */

"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Switch component props
 */
interface SwitchProps {
  /** Whether the switch is checked */
  checked?: boolean;
  /** Callback when switch state changes */
  onCheckedChange?: (checked: boolean) => void;
  /** Whether the switch is disabled */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Switch size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Accessible label for screen readers */
  'aria-label'?: string;
  /** ID for the switch input */
  id?: string;
}

// ============================================================================
// SWITCH COMPONENT
// ============================================================================

/**
 * Switch component with accessibility support
 * 
 * @example
 * ```tsx
 * <Switch 
 *   checked={enabled} 
 *   onCheckedChange={setEnabled}
 *   aria-label="Enable notifications"
 * />
 * ```
 */
const Switch = React.forwardRef<HTMLButtonElement, SwitchProps>(
  ({ 
    checked = false, 
    onCheckedChange, 
    disabled = false, 
    className,
    size = 'md',
    'aria-label': ariaLabel,
    id,
    ...props 
  }, ref) => {
    /**
     * Handle switch toggle
     */
    const handleToggle = () => {
      if (!disabled && onCheckedChange) {
        onCheckedChange(!checked);
      }
    };

    /**
     * Handle keyboard interaction
     */
    const handleKeyDown = (event: React.KeyboardEvent) => {
      if (event.key === ' ' || event.key === 'Enter') {
        event.preventDefault();
        handleToggle();
      }
    };

    /**
     * Get size-specific classes
     */
    const getSizeClasses = () => {
      switch (size) {
        case 'sm':
          return {
            track: 'h-4 w-7',
            thumb: 'h-3 w-3 data-[state=checked]:translate-x-3'
          };
        case 'lg':
          return {
            track: 'h-7 w-12',
            thumb: 'h-6 w-6 data-[state=checked]:translate-x-5'
          };
        default: // md
          return {
            track: 'h-5 w-9',
            thumb: 'h-4 w-4 data-[state=checked]:translate-x-4'
          };
      }
    };

    const sizeClasses = getSizeClasses();

    return (
      <button
        ref={ref}
        type="button"
        role="switch"
        aria-checked={checked}
        aria-label={ariaLabel}
        id={id}
        data-state={checked ? 'checked' : 'unchecked'}
        data-disabled={disabled ? '' : undefined}
        disabled={disabled}
        className={cn(
          // Base track styles
          "peer inline-flex shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background",
          "disabled:cursor-not-allowed disabled:opacity-50",
          // Track size
          sizeClasses.track,
          // Track colors
          checked 
            ? "bg-primary" 
            : "bg-input",
          className
        )}
        onClick={handleToggle}
        onKeyDown={handleKeyDown}
        {...props}
      >
        {/* Thumb */}
        <span
          data-state={checked ? 'checked' : 'unchecked'}
          className={cn(
            // Base thumb styles
            "pointer-events-none block rounded-full bg-background shadow-lg ring-0 transition-transform",
            // Thumb size and position
            sizeClasses.thumb,
            // Initial position
            checked ? undefined : "translate-x-0"
          )}
        />
      </button>
    );
  }
);

Switch.displayName = "Switch";

// ============================================================================
// SWITCH WITH LABEL COMPONENT
// ============================================================================

/**
 * Switch with label component props
 */
interface SwitchWithLabelProps extends SwitchProps {
  /** Label text */
  label: string;
  /** Optional description text */
  description?: string;
  /** Label position relative to switch */
  labelPosition?: 'left' | 'right';
}

/**
 * Switch component with integrated label
 * 
 * @example
 * ```tsx
 * <SwitchWithLabel
 *   label="Dark mode"
 *   description="Use dark theme"
 *   checked={darkMode}
 *   onCheckedChange={setDarkMode}
 * />
 * ```
 */
export function SwitchWithLabel({
  label,
  description,
  labelPosition = 'left',
  className,
  id: providedId,
  ...switchProps
}: SwitchWithLabelProps) {
  const id = providedId || React.useId();

  const switchElement = (
    <Switch
      id={id}
      aria-label={label}
      {...switchProps}
    />
  );

  const labelElement = (
    <div className="grid gap-1.5 leading-none">
      <label
        htmlFor={id}
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
      >
        {label}
      </label>
      {description && (
        <p className="text-xs text-muted-foreground">
          {description}
        </p>
      )}
    </div>
  );

  return (
    <div className={cn("flex items-center space-x-3", className)}>
      {labelPosition === 'left' ? (
        <>
          {labelElement}
          {switchElement}
        </>
      ) : (
        <>
          {switchElement}
          {labelElement}
        </>
      )}
    </div>
  );
}

export { Switch };
