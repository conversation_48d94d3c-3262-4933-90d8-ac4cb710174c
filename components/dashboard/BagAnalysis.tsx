/**
 * BagAnalysis Component for Disc Golf Inventory Management System
 *
 * Analyzes current bag composition, identifies missing disc types, and provides
 * recommendations based on flight numbers and disc type coverage.
 */

"use client";

import * as React from "react";
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { 
  analyzeBagComposition, 
  DiscType, 
  StabilityType,
  type BagComposition,
  type BagRecommendation 
} from "@/lib/discUtils";
import type { Disc } from "@/lib/types";
import { 
  Target, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp,
  Disc3,
  Zap,
  Wind,
  Navigation
} from "lucide-react";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface BagAnalysisProps {
  discs: Disc[];
  className?: string;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get icon for disc type
 */
function getDiscTypeIcon(type: DiscType) {
  switch (type) {
    case DiscType.PUTTER:
      return Target;
    case DiscType.MIDRANGE:
      return Navigation;
    case DiscType.FAIRWAY_DRIVER:
      return Wind;
    case DiscType.DISTANCE_DRIVER:
      return Zap;
    default:
      return Disc3;
  }
}

/**
 * Get color for stability type
 */
function getStabilityColor(stability: StabilityType): string {
  switch (stability) {
    case StabilityType.OVERSTABLE:
      return "bg-red-100 text-red-800 border-red-200";
    case StabilityType.STABLE:
      return "bg-blue-100 text-blue-800 border-blue-200";
    case StabilityType.UNDERSTABLE:
      return "bg-green-100 text-green-800 border-green-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
}

/**
 * Get priority color for recommendations
 */
function getPriorityColor(priority: BagRecommendation["priority"]): string {
  switch (priority) {
    case "high":
      return "bg-red-100 text-red-800 border-red-200";
    case "medium":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "low":
      return "bg-blue-100 text-blue-800 border-blue-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
}

/**
 * Format disc type for display
 */
function formatDiscType(type: DiscType): string {
  switch (type) {
    case DiscType.PUTTER:
      return "Putter";
    case DiscType.MIDRANGE:
      return "Midrange";
    case DiscType.FAIRWAY_DRIVER:
      return "Fairway Driver";
    case DiscType.DISTANCE_DRIVER:
      return "Distance Driver";
    default:
      return type;
  }
}

/**
 * Format stability type for display
 */
function formatStabilityType(stability: StabilityType): string {
  switch (stability) {
    case StabilityType.OVERSTABLE:
      return "Overstable";
    case StabilityType.STABLE:
      return "Stable";
    case StabilityType.UNDERSTABLE:
      return "Understable";
    default:
      return stability;
  }
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * BagAnalysis component for analyzing bag composition and providing recommendations
 *
 * @example
 * ```tsx
 * <BagAnalysis discs={discs} />
 * ```
 */
export function BagAnalysis({ discs, className }: BagAnalysisProps) {
  const bagComposition = React.useMemo(() => analyzeBagComposition(discs), [discs]);

  if (bagComposition.totalDiscs === 0) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <Target className="h-5 w-5" />
            Bag Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Disc3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No discs in bag</p>
            <p className="text-sm text-muted-foreground mt-1">
              Add discs to your bag to see composition analysis
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Bag Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <Target className="h-5 w-5" />
            Bag Composition ({bagComposition.totalDiscs} discs)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Disc Types */}
          <div>
            <h4 className="text-sm font-medium mb-3">By Disc Type</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {Object.entries(bagComposition.byType).map(([type, count]) => {
                const Icon = getDiscTypeIcon(type as DiscType);
                return (
                  <div key={type} className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                    <Icon className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">{count}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDiscType(type as DiscType)}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Stability Distribution */}
          <div>
            <h4 className="text-sm font-medium mb-3">By Stability</h4>
            <div className="flex flex-wrap gap-2">
              {Object.entries(bagComposition.byStability).map(([stability, count]) => (
                <Badge 
                  key={stability} 
                  variant="outline" 
                  className={getStabilityColor(stability as StabilityType)}
                >
                  {formatStabilityType(stability as StabilityType)}: {count}
                </Badge>
              ))}
            </div>
          </div>

          {/* Speed Coverage */}
          <div>
            <h4 className="text-sm font-medium mb-3">Speed Coverage</h4>
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Range:</span>
                <Badge variant="outline">
                  {bagComposition.speedCoverage.min} - {bagComposition.speedCoverage.max}
                </Badge>
              </div>
              {bagComposition.speedCoverage.gaps.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">Gaps:</span>
                  <div className="flex gap-1">
                    {bagComposition.speedCoverage.gaps.slice(0, 3).map(gap => (
                      <Badge key={gap} variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                        {gap}
                      </Badge>
                    ))}
                    {bagComposition.speedCoverage.gaps.length > 3 && (
                      <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                        +{bagComposition.speedCoverage.gaps.length - 3}
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      {bagComposition.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <TrendingUp className="h-5 w-5" />
              Bag Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {bagComposition.recommendations.slice(0, 5).map((rec, index) => {
                const Icon = rec.priority === "high" ? AlertTriangle : 
                           rec.priority === "medium" ? TrendingUp : CheckCircle;
                
                return (
                  <div key={index} className="flex items-start gap-3 p-3 bg-muted rounded-lg">
                    <Icon className={cn(
                      "h-4 w-4 mt-0.5",
                      rec.priority === "high" ? "text-red-500" :
                      rec.priority === "medium" ? "text-yellow-500" : "text-blue-500"
                    )} />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="text-sm font-medium">
                          {formatDiscType(rec.type)} - {formatStabilityType(rec.stability)}
                        </p>
                        <Badge 
                          variant="outline" 
                          className={getPriorityColor(rec.priority)}
                        >
                          {rec.priority}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">{rec.reason}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Suggested speed: {rec.suggestedSpeed}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
