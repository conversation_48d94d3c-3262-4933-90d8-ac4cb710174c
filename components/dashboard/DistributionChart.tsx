/**
 * DistributionChart Component for Disc Golf Inventory Management System
 *
 * A component for displaying data distributions with visual charts,
 * progress bars, and responsive design for dashboard layouts.
 */

"use client";

import * as React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface DistributionItem {
  label: string;
  count: number;
  percentage: number;
  color?: string;
}

export interface DistributionChartProps {
  title: string;
  description?: string;
  data: DistributionItem[];
  icon?: React.ComponentType<{ className?: string }>;
  variant?: "bar" | "list" | "compact";
  maxItems?: number;
  showPercentages?: boolean;
  showCounts?: boolean;
  className?: string;
  /** Enable interactive features like hover effects and tooltips */
  interactive?: boolean;
  /** Callback when an item is clicked */
  onItemClick?: (item: DistributionItem, index: number) => void;
  /** Show detailed tooltips on hover */
  showTooltips?: boolean;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Generate a color for an item based on its index
 */
function generateColor(index: number): string {
  const hue = (index * 60) % 360;
  return `hsl(${hue}, 70%, 50%)`;
}

/**
 * Get a lighter version of a color for backgrounds
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function getLighterColor(color: string): string {
  if (color.startsWith("hsl")) {
    return color.replace("50%)", "90%)");
  }
  return color;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * DistributionChart component for displaying data distributions
 *
 * @example
 * ```tsx
 * <DistributionChart
 *   title="By Manufacturer"
 *   data={manufacturerData}
 *   icon={Package}
 *   variant="bar"
 *   maxItems={5}
 * />
 * ```
 */
export function DistributionChart({
  title,
  description,
  data,
  icon: Icon,
  variant = "list",
  maxItems = 5,
  showPercentages = true,
  showCounts = true,
  className,
  interactive = true,
  onItemClick,
  showTooltips = true,
}: DistributionChartProps) {
  const displayData = data.slice(0, maxItems);
  const remainingCount = data.length - maxItems;

  if (data.length === 0) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            {Icon && <Icon className="h-5 w-5" />}
            {title}
          </CardTitle>
          {description && <p className="text-sm text-muted-foreground">{description}</p>}
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-sm text-muted-foreground">No data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (variant === "compact") {
    return (
      <Card className={cn("", className)}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium">{title}</h4>
            {Icon && <Icon className="h-4 w-4 text-muted-foreground" />}
          </div>
          <div className="space-y-2">
            {displayData.map((item, index) => {
              const color = item.color || generateColor(index);
              const tooltipContent = showTooltips
                ? `${item.label}: ${item.count} (${item.percentage.toFixed(1)}%)`
                : "";

              const compactElement = (
                <div
                  key={item.label}
                  className={cn(
                    "flex items-center justify-between text-sm",
                    interactive && onItemClick && "cursor-pointer",
                    interactive && "hover:bg-muted/50 rounded-md p-1 -m-1 transition-colors duration-200"
                  )}
                  onClick={() => interactive && onItemClick?.(item, index)}
                  role={interactive && onItemClick ? "button" : undefined}
                  tabIndex={interactive && onItemClick ? 0 : undefined}
                  onKeyDown={(e) => {
                    if (interactive && onItemClick && (e.key === "Enter" || e.key === " ")) {
                      e.preventDefault();
                      onItemClick(item, index);
                    }
                  }}
                >
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        "w-2 h-2 rounded-full transition-all duration-200",
                        interactive && "hover:scale-110"
                      )}
                      style={{ backgroundColor: color }}
                    />
                    <span className="truncate">{item.label}</span>
                  </div>
                  <span className="font-medium">{item.count}</span>
                </div>
              );

              return showTooltips && interactive ? (
                <Tooltip key={item.label} content={tooltipContent}>
                  {compactElement}
                </Tooltip>
              ) : (
                compactElement
              );
            })}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (variant === "bar") {
    const maxCount = Math.max(...data.map((item) => item.count));

    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            {Icon && <Icon className="h-5 w-5" />}
            {title}
          </CardTitle>
          {description && <p className="text-sm text-muted-foreground">{description}</p>}
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {displayData.map((item, index) => {
              const color = item.color || generateColor(index);
              const width = maxCount > 0 ? (item.count / maxCount) * 100 : 0;

              const tooltipContent = showTooltips
                ? `${item.label}: ${item.count} (${item.percentage.toFixed(1)}%)`
                : "";

              const barElement = (
                <div
                  key={item.label}
                  className={cn(
                    "space-y-1",
                    interactive && onItemClick && "cursor-pointer",
                    interactive && "hover:bg-muted/50 rounded-md p-2 -m-2 transition-colors duration-200"
                  )}
                  onClick={() => interactive && onItemClick?.(item, index)}
                  role={interactive && onItemClick ? "button" : undefined}
                  tabIndex={interactive && onItemClick ? 0 : undefined}
                  onKeyDown={(e) => {
                    if (interactive && onItemClick && (e.key === "Enter" || e.key === " ")) {
                      e.preventDefault();
                      onItemClick(item, index);
                    }
                  }}
                >
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">{item.label}</span>
                    <div className="flex items-center gap-2">
                      {showCounts && <span className="text-muted-foreground">{item.count}</span>}
                      {showPercentages && (
                        <span className="text-muted-foreground">({item.percentage.toFixed(1)}%)</span>
                      )}
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={cn("h-2 rounded-full transition-all duration-300", interactive && "hover:opacity-80")}
                      style={{
                        backgroundColor: color,
                        width: `${width}%`,
                      }}
                    />
                  </div>
                </div>
              );

              return showTooltips && interactive ? (
                <Tooltip key={item.label} content={tooltipContent}>
                  {barElement}
                </Tooltip>
              ) : (
                barElement
              );
            })}
            {remainingCount > 0 && (
              <div className="text-xs text-muted-foreground pt-2 border-t">
                +{remainingCount} more {remainingCount === 1 ? "item" : "items"}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Default list variant
  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-base">
          {Icon && <Icon className="h-5 w-5" />}
          {title}
        </CardTitle>
        {description && <p className="text-sm text-muted-foreground">{description}</p>}
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {displayData.map((item, index) => {
            const color = item.color || generateColor(index);
            const tooltipContent = showTooltips ? `${item.label}: ${item.count} (${item.percentage.toFixed(1)}%)` : "";

            const listElement = (
              <div
                key={item.label}
                className={cn(
                  "flex items-center justify-between",
                  interactive && onItemClick && "cursor-pointer",
                  interactive && "hover:bg-muted/50 rounded-md p-2 -m-2 transition-colors duration-200"
                )}
                onClick={() => interactive && onItemClick?.(item, index)}
                role={interactive && onItemClick ? "button" : undefined}
                tabIndex={interactive && onItemClick ? 0 : undefined}
                onKeyDown={(e) => {
                  if (interactive && onItemClick && (e.key === "Enter" || e.key === " ")) {
                    e.preventDefault();
                    onItemClick(item, index);
                  }
                }}
              >
                <div className="flex items-center gap-2">
                  <div
                    className={cn("w-3 h-3 rounded-full transition-all duration-200", interactive && "hover:scale-110")}
                    style={{ backgroundColor: color }}
                  />
                  <span className="text-sm font-medium">{item.label}</span>
                </div>
                <div className="text-right">
                  {showCounts && <div className="text-sm font-bold">{item.count}</div>}
                  {showPercentages && (
                    <div className="text-xs text-muted-foreground">{item.percentage.toFixed(1)}%</div>
                  )}
                </div>
              </div>
            );

            return showTooltips && interactive ? (
              <Tooltip key={item.label} content={tooltipContent}>
                {listElement}
              </Tooltip>
            ) : (
              listElement
            );
          })}
          {remainingCount > 0 && (
            <div className="text-xs text-muted-foreground pt-2 border-t">
              +{remainingCount} more {remainingCount === 1 ? "category" : "categories"}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// ============================================================================
// UTILITY COMPONENTS
// ============================================================================

/**
 * DistributionGrid component for organizing multiple distribution charts
 */
export interface DistributionGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3;
  className?: string;
}

export function DistributionGrid({ children, columns = 3, className }: DistributionGridProps) {
  const gridClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  };

  return <div className={cn("grid gap-6", gridClasses[columns], className)}>{children}</div>;
}
