/**
 * Tests for BagAnalysis Component
 *
 * Comprehensive test suite for the BagAnalysis component including
 * rendering, user interactions, and data analysis display.
 */

import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { BagAnalysis } from "../BagAnalysis";
import type { Disc } from "@/lib/types";
import { DiscCondition, Location } from "@/lib/types";

// ============================================================================
// TEST DATA HELPERS
// ============================================================================

const createTestDisc = (overrides: Partial<Disc> = {}): Disc => ({
  id: "test-id",
  manufacturer: "Test Manufacturer",
  mold: "Test Mold",
  plasticType: "Test Plastic",
  weight: 175,
  condition: DiscCondition.GOOD,
  flightNumbers: { speed: 5, glide: 5, turn: 0, fade: 2 },
  color: "Blue",
  currentLocation: Location.HOME,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

// ============================================================================
// COMPONENT TESTS
// ============================================================================

describe("BagAnalysis", () => {
  it("should render empty state when no discs provided", () => {
    render(<BagAnalysis discs={[]} />);
    
    expect(screen.getByText("Bag Analysis")).toBeInTheDocument();
    expect(screen.getByText("No discs in bag")).toBeInTheDocument();
    expect(screen.getByText("Add discs to your bag to see composition analysis")).toBeInTheDocument();
  });

  it("should render empty state when no discs in bag", () => {
    const discs = [
      createTestDisc({ currentLocation: Location.HOME }),
      createTestDisc({ currentLocation: Location.CAR }),
    ];
    
    render(<BagAnalysis discs={discs} />);
    
    expect(screen.getByText("No discs in bag")).toBeInTheDocument();
  });

  it("should render bag composition when discs are in bag", () => {
    const discs = [
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 2, glide: 3, turn: 0, fade: 1 } 
      }),
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 5, glide: 5, turn: 0, fade: 2 } 
      }),
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 9, glide: 5, turn: -1, fade: 2 } 
      }),
    ];
    
    render(<BagAnalysis discs={discs} />);
    
    expect(screen.getByText("Bag Composition (3 discs)")).toBeInTheDocument();
    expect(screen.getByText("By Disc Type")).toBeInTheDocument();
    expect(screen.getByText("By Stability")).toBeInTheDocument();
    expect(screen.getByText("Speed Coverage")).toBeInTheDocument();
  });

  it("should display disc type distribution correctly", () => {
    const discs = [
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 2, glide: 3, turn: 0, fade: 1 } // Putter
      }),
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 5, glide: 5, turn: 0, fade: 2 } // Midrange
      }),
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 9, glide: 5, turn: -1, fade: 2 } // Fairway Driver
      }),
    ];
    
    render(<BagAnalysis discs={discs} />);
    
    expect(screen.getByText("Putter")).toBeInTheDocument();
    expect(screen.getByText("Midrange")).toBeInTheDocument();
    expect(screen.getByText("Fairway Driver")).toBeInTheDocument();
  });

  it("should display stability distribution with badges", () => {
    const discs = [
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 7, glide: 5, turn: 0, fade: 3 } // Overstable
      }),
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 5, glide: 5, turn: 0, fade: 2 } // Stable
      }),
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 6, glide: 5, turn: -2, fade: 1 } // Understable
      }),
    ];
    
    render(<BagAnalysis discs={discs} />);
    
    expect(screen.getByText(/Overstable:/)).toBeInTheDocument();
    expect(screen.getByText(/Stable:/)).toBeInTheDocument();
    expect(screen.getByText(/Understable:/)).toBeInTheDocument();
  });

  it("should display speed coverage information", () => {
    const discs = [
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 2, glide: 3, turn: 0, fade: 1 }
      }),
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 9, glide: 5, turn: -1, fade: 2 }
      }),
    ];
    
    render(<BagAnalysis discs={discs} />);
    
    expect(screen.getByText("Range:")).toBeInTheDocument();
    expect(screen.getByText("2 - 9")).toBeInTheDocument();
    expect(screen.getByText("Gaps:")).toBeInTheDocument();
  });

  it("should display recommendations when gaps exist", () => {
    const discs = [
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 9, glide: 5, turn: -1, fade: 2 } // Only fairway driver
      }),
    ];
    
    render(<BagAnalysis discs={discs} />);
    
    expect(screen.getByText("Bag Recommendations")).toBeInTheDocument();
    expect(screen.getByText(/No putters in bag/)).toBeInTheDocument();
    expect(screen.getByText(/No midranges in bag/)).toBeInTheDocument();
  });

  it("should display recommendation priorities correctly", () => {
    const discs = [
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 9, glide: 5, turn: -1, fade: 2 }
      }),
    ];
    
    render(<BagAnalysis discs={discs} />);
    
    // Should have high priority recommendations for missing putters and midranges
    const highPriorityBadges = screen.getAllByText("high");
    expect(highPriorityBadges.length).toBeGreaterThan(0);
  });

  it("should limit recommendations display to 5 items", () => {
    const discs = [
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 12, glide: 5, turn: -1, fade: 3 } // Only distance driver
      }),
    ];
    
    render(<BagAnalysis discs={discs} />);
    
    // Should have recommendations but limited to 5
    const recommendationItems = screen.getAllByRole("generic").filter(
      el => el.className?.includes("p-3 bg-muted rounded-lg")
    );
    expect(recommendationItems.length).toBeLessThanOrEqual(5);
  });

  it("should apply custom className", () => {
    const { container } = render(<BagAnalysis discs={[]} className="custom-class" />);
    
    expect(container.firstChild).toHaveClass("custom-class");
  });

  it("should be accessible with proper ARIA labels", () => {
    const discs = [
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 5, glide: 5, turn: 0, fade: 2 }
      }),
    ];
    
    render(<BagAnalysis discs={discs} />);
    
    // Check for proper heading structure
    expect(screen.getByRole("heading", { level: 4 })).toBeInTheDocument();
  });

  it("should handle edge cases gracefully", () => {
    const discs = [
      createTestDisc({ 
        currentLocation: Location.BAG, 
        flightNumbers: { speed: 1, glide: 1, turn: -5, fade: 0 } // Edge case values
      }),
    ];
    
    expect(() => render(<BagAnalysis discs={discs} />)).not.toThrow();
  });
});
