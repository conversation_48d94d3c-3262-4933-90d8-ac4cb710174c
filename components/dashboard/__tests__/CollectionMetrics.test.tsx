/**
 * Tests for CollectionMetrics Component
 *
 * Comprehensive test suite for the CollectionMetrics component including
 * rendering, health score calculations, and metrics display.
 */

import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import { CollectionMetrics } from "../CollectionMetrics";
import type { Disc } from "@/lib/types";
import { DiscCondition, Location } from "@/lib/types";

// ============================================================================
// TEST DATA HELPERS
// ============================================================================

const createTestDisc = (overrides: Partial<Disc> = {}): Disc => ({
  id: "test-id",
  manufacturer: "Test Manufacturer",
  mold: "Test Mold",
  plasticType: "Test Plastic",
  weight: 175,
  condition: DiscCondition.GOOD,
  flightNumbers: { speed: 5, glide: 5, turn: 0, fade: 2 },
  color: "Blue",
  currentLocation: Location.HOME,
  purchasePrice: 20,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

// ============================================================================
// COMPONENT TESTS
// ============================================================================

describe("CollectionMetrics", () => {
  it("should render empty state when no discs provided", () => {
    render(<CollectionMetrics discs={[]} />);
    
    expect(screen.getByText("Collection Metrics")).toBeInTheDocument();
    expect(screen.getByText("No collection data")).toBeInTheDocument();
    expect(screen.getByText("Add discs to see collection health metrics")).toBeInTheDocument();
  });

  it("should render collection health score", () => {
    const discs = [
      createTestDisc({ condition: DiscCondition.NEW }),
      createTestDisc({ condition: DiscCondition.GOOD }),
    ];
    
    render(<CollectionMetrics discs={discs} />);
    
    expect(screen.getByText("Collection Health")).toBeInTheDocument();
    expect(screen.getByText("Overall Score")).toBeInTheDocument();
    
    // Should display a numeric score
    const scoreElement = screen.getByText(/^\d+$/);
    expect(scoreElement).toBeInTheDocument();
  });

  it("should display health status based on score", () => {
    const excellentDiscs = [
      createTestDisc({ condition: DiscCondition.NEW }),
      createTestDisc({ condition: DiscCondition.NEW }),
    ];
    
    render(<CollectionMetrics discs={excellentDiscs} />);
    
    // Should show positive status for good condition discs
    expect(screen.getByText(/Excellent|Good/)).toBeInTheDocument();
  });

  it("should display diversity score", () => {
    const diverseDiscs = [
      createTestDisc({ 
        manufacturer: "Innova", 
        mold: "Destroyer", 
        plasticType: "Champion" 
      }),
      createTestDisc({ 
        manufacturer: "Discraft", 
        mold: "Buzzz", 
        plasticType: "ESP" 
      }),
      createTestDisc({ 
        manufacturer: "Dynamic", 
        mold: "Judge", 
        plasticType: "Lucid" 
      }),
    ];
    
    render(<CollectionMetrics discs={diverseDiscs} />);
    
    expect(screen.getByText(/diversity/i)).toBeInTheDocument();
  });

  it("should display usage efficiency", () => {
    const discs = [
      createTestDisc({ currentLocation: Location.BAG }),
      createTestDisc({ currentLocation: Location.HOME }),
    ];
    
    render(<CollectionMetrics discs={discs} />);
    
    expect(screen.getByText(/usage/i)).toBeInTheDocument();
    expect(screen.getByText(/50%/)).toBeInTheDocument(); // 1 out of 2 in bag
  });

  it("should display value at risk", () => {
    const discs = [
      createTestDisc({ 
        condition: DiscCondition.WORN, 
        purchasePrice: 25 
      }),
      createTestDisc({ 
        condition: DiscCondition.DAMAGED, 
        purchasePrice: 15 
      }),
      createTestDisc({ 
        condition: DiscCondition.GOOD, 
        purchasePrice: 20 
      }),
    ];
    
    render(<CollectionMetrics discs={discs} />);
    
    expect(screen.getByText("Value at Risk")).toBeInTheDocument();
    expect(screen.getByText("$40")).toBeInTheDocument(); // 25 + 15
  });

  it("should display condition distribution", () => {
    const discs = [
      createTestDisc({ condition: DiscCondition.NEW }),
      createTestDisc({ condition: DiscCondition.GOOD }),
      createTestDisc({ condition: DiscCondition.WORN }),
    ];
    
    render(<CollectionMetrics discs={discs} />);
    
    expect(screen.getByText("Condition Distribution")).toBeInTheDocument();
    expect(screen.getByText("New")).toBeInTheDocument();
    expect(screen.getByText("Good")).toBeInTheDocument();
    expect(screen.getByText("Worn")).toBeInTheDocument();
  });

  it("should show condition percentages correctly", () => {
    const discs = [
      createTestDisc({ condition: DiscCondition.NEW }),
      createTestDisc({ condition: DiscCondition.NEW }),
      createTestDisc({ condition: DiscCondition.GOOD }),
      createTestDisc({ condition: DiscCondition.GOOD }),
    ];
    
    render(<CollectionMetrics discs={discs} />);
    
    // Should show 50% for both NEW and GOOD conditions
    const percentageElements = screen.getAllByText(/50%/);
    expect(percentageElements.length).toBeGreaterThan(0);
  });

  it("should display replacement recommendations", () => {
    const discs = [
      createTestDisc({ 
        condition: DiscCondition.WORN,
        manufacturer: "Innova",
        mold: "Destroyer"
      }),
      createTestDisc({ 
        condition: DiscCondition.DAMAGED,
        manufacturer: "Discraft", 
        mold: "Buzzz"
      }),
      createTestDisc({ condition: DiscCondition.GOOD }),
    ];
    
    render(<CollectionMetrics discs={discs} />);
    
    expect(screen.getByText("Replacement Recommendations")).toBeInTheDocument();
    expect(screen.getByText("Innova Destroyer")).toBeInTheDocument();
    expect(screen.getByText("Discraft Buzzz")).toBeInTheDocument();
  });

  it("should limit replacement recommendations to 5 items", () => {
    const discs = Array.from({ length: 10 }, (_, i) => 
      createTestDisc({ 
        id: `disc-${i}`,
        condition: DiscCondition.WORN,
        manufacturer: `Manufacturer ${i}`,
        mold: `Mold ${i}`
      })
    );
    
    render(<CollectionMetrics discs={discs} />);
    
    // Should show "+X more discs need attention" message
    expect(screen.getByText(/\+\d+ more discs need attention/)).toBeInTheDocument();
  });

  it("should not show replacement section when no discs need replacement", () => {
    const discs = [
      createTestDisc({ condition: DiscCondition.NEW }),
      createTestDisc({ condition: DiscCondition.GOOD }),
    ];
    
    render(<CollectionMetrics discs={discs} />);
    
    expect(screen.queryByText("Replacement Recommendations")).not.toBeInTheDocument();
  });

  it("should display progress bars for condition distribution", () => {
    const discs = [
      createTestDisc({ condition: DiscCondition.NEW }),
      createTestDisc({ condition: DiscCondition.GOOD }),
    ];
    
    const { container } = render(<CollectionMetrics discs={discs} />);
    
    // Should have progress bar elements
    const progressBars = container.querySelectorAll('.bg-primary');
    expect(progressBars.length).toBeGreaterThan(0);
  });

  it("should apply custom className", () => {
    const { container } = render(<CollectionMetrics discs={[]} className="custom-class" />);
    
    expect(container.firstChild).toHaveClass("custom-class");
  });

  it("should handle discs without purchase prices", () => {
    const discs = [
      createTestDisc({ 
        condition: DiscCondition.WORN,
        purchasePrice: undefined
      }),
    ];
    
    expect(() => render(<CollectionMetrics discs={discs} />)).not.toThrow();
    expect(screen.getByText("$0")).toBeInTheDocument(); // Value at risk should be 0
  });

  it("should be accessible with proper heading structure", () => {
    const discs = [createTestDisc()];
    
    render(<CollectionMetrics discs={discs} />);
    
    // Check for proper heading levels
    expect(screen.getAllByRole("heading")).toHaveLength(3); // Collection Health, Condition Distribution, and potentially Replacement Recommendations
  });

  it("should handle edge cases gracefully", () => {
    const edgeCaseDiscs = [
      createTestDisc({ 
        manufacturer: "",
        mold: "",
        condition: DiscCondition.DAMAGED,
        purchasePrice: 0
      }),
    ];
    
    expect(() => render(<CollectionMetrics discs={edgeCaseDiscs} />)).not.toThrow();
  });
});
