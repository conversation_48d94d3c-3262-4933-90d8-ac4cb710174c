/**
 * CollectionMetrics Component for Disc Golf Inventory Management System
 *
 * Displays advanced analytics for collection health, value trends, diversity metrics,
 * and usage patterns with visual indicators and actionable insights.
 */

"use client";

import * as React from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { 
  calculateCollectionHealth,
  type CollectionHealth 
} from "@/lib/discUtils";
import type { Disc, DiscCondition } from "@/lib/types";
import { 
  Heart,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  Package,
  Target,
  BarChart3,
  Zap,
  Shield
} from "lucide-react";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface CollectionMetricsProps {
  discs: Disc[];
  className?: string;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get health score color based on score value
 */
function getHealthScoreColor(score: number): string {
  if (score >= 80) return "text-green-600";
  if (score >= 60) return "text-yellow-600";
  return "text-red-600";
}

/**
 * Get health score icon based on score value
 */
function getHealthScoreIcon(score: number) {
  if (score >= 80) return CheckCircle;
  if (score >= 60) return AlertTriangle;
  return AlertTriangle;
}

/**
 * Format condition text for display
 */
function formatConditionText(condition: DiscCondition): string {
  switch (condition) {
    case "new":
      return "New";
    case "good":
      return "Good";
    case "fair":
      return "Fair";
    case "worn":
      return "Worn";
    case "damaged":
      return "Damaged";
    default:
      return condition;
  }
}

/**
 * Get condition color
 */
function getConditionColor(condition: DiscCondition): string {
  switch (condition) {
    case "new":
      return "bg-green-100 text-green-800 border-green-200";
    case "good":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "fair":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "worn":
      return "bg-orange-100 text-orange-800 border-orange-200";
    case "damaged":
      return "bg-red-100 text-red-800 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
}

/**
 * Get diversity score description
 */
function getDiversityDescription(score: number): string {
  if (score >= 80) return "Excellent diversity";
  if (score >= 60) return "Good diversity";
  if (score >= 40) return "Moderate diversity";
  return "Limited diversity";
}

/**
 * Get usage efficiency description
 */
function getUsageDescription(efficiency: number): string {
  if (efficiency >= 80) return "High usage";
  if (efficiency >= 60) return "Good usage";
  if (efficiency >= 40) return "Moderate usage";
  return "Low usage";
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * CollectionMetrics component for displaying advanced collection analytics
 *
 * @example
 * ```tsx
 * <CollectionMetrics discs={discs} />
 * ```
 */
export function CollectionMetrics({ discs, className }: CollectionMetricsProps) {
  const collectionHealth = React.useMemo(() => calculateCollectionHealth(discs), [discs]);

  if (discs.length === 0) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <BarChart3 className="h-5 w-5" />
            Collection Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No collection data</p>
            <p className="text-sm text-muted-foreground mt-1">
              Add discs to see collection health metrics
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const HealthIcon = getHealthScoreIcon(collectionHealth.overallScore);

  return (
    <div className={cn("space-y-6", className)}>
      {/* Overall Health Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <Heart className="h-5 w-5" />
            Collection Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <HealthIcon className={cn("h-8 w-8", getHealthScoreColor(collectionHealth.overallScore))} />
              <div>
                <p className={cn("text-3xl font-bold", getHealthScoreColor(collectionHealth.overallScore))}>
                  {collectionHealth.overallScore}
                </p>
                <p className="text-sm text-muted-foreground">Overall Score</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium">
                {collectionHealth.overallScore >= 80 ? "Excellent" :
                 collectionHealth.overallScore >= 60 ? "Good" :
                 collectionHealth.overallScore >= 40 ? "Fair" : "Needs Attention"}
              </p>
              <p className="text-xs text-muted-foreground">Collection Status</p>
            </div>
          </div>

          {/* Health Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
              <Package className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-lg font-semibold">{collectionHealth.diversityScore}</p>
                <p className="text-xs text-muted-foreground">
                  {getDiversityDescription(collectionHealth.diversityScore)}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
              <Target className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-lg font-semibold">{collectionHealth.usageEfficiency}%</p>
                <p className="text-xs text-muted-foreground">
                  {getUsageDescription(collectionHealth.usageEfficiency)}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
              <DollarSign className="h-5 w-5 text-yellow-500" />
              <div>
                <p className="text-lg font-semibold">
                  ${collectionHealth.valueAtRisk.toFixed(0)}
                </p>
                <p className="text-xs text-muted-foreground">Value at Risk</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Condition Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <Shield className="h-5 w-5" />
            Condition Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(collectionHealth.conditionDistribution)
              .filter(([_, count]) => count > 0)
              .sort(([, a], [, b]) => b - a)
              .map(([condition, count]) => {
                const percentage = Math.round((count / discs.length) * 100);
                return (
                  <div key={condition} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Badge 
                        variant="outline" 
                        className={getConditionColor(condition as DiscCondition)}
                      >
                        {formatConditionText(condition as DiscCondition)}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {count} discs ({percentage}%)
                      </span>
                    </div>
                    <div className="w-24 bg-muted rounded-full h-2">
                      <div 
                        className="h-2 rounded-full bg-primary" 
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                );
              })}
          </div>
        </CardContent>
      </Card>

      {/* Replacement Recommendations */}
      {collectionHealth.replacementNeeded.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              Replacement Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {collectionHealth.replacementNeeded.slice(0, 5).map((disc) => (
                <div key={disc.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {disc.manufacturer} {disc.mold}
                    </p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge 
                        variant="outline" 
                        className={getConditionColor(disc.condition)}
                      >
                        {formatConditionText(disc.condition)}
                      </Badge>
                      {disc.purchasePrice && (
                        <span className="text-xs text-muted-foreground">
                          ${disc.purchasePrice}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-muted-foreground">
                      Speed {disc.flightNumbers.speed}
                    </p>
                  </div>
                </div>
              ))}
              {collectionHealth.replacementNeeded.length > 5 && (
                <p className="text-xs text-muted-foreground text-center pt-2">
                  +{collectionHealth.replacementNeeded.length - 5} more discs need attention
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
