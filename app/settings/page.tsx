/**
 * Settings Page for Disc Golf Inventory Management System
 *
 * Main settings page with tabbed interface for appearance, data, and advanced settings.
 * Implements immediate settings application and localStorage persistence.
 */

"use client";

import * as React from "react";
import { Layout, PageContainer } from "@/components/layout/Layout";
import { Tabs, TabContent } from "@/components/ui/tabs";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import { AppSettingsType, validateAppSettings } from "@/lib/validation";
import { Settings, Palette, Database, Zap } from "lucide-react";

// Lazy load settings components for better performance
const AppearanceSettings = React.lazy(() =>
  import("@/components/settings/AppearanceSettings").then((mod) => ({ default: mod.AppearanceSettings }))
);
const DataSettings = React.lazy(() =>
  import("@/components/settings/DataSettings").then((mod) => ({ default: mod.DataSettings }))
);
const AdvancedSettings = React.lazy(() =>
  import("@/components/settings/AdvancedSettings").then((mod) => ({ default: mod.AdvancedSettings }))
);

// ============================================================================
// CONSTANTS
// ============================================================================

/**
 * Default settings configuration
 */
const DEFAULT_SETTINGS: AppSettingsType = {
  appearance: {
    theme: "system",
    compactMode: false,
    showFlightNumbers: true,
    defaultView: "grid",
  },
  data: {
    autoBackup: false,
    backupFrequency: "weekly",
    maxStorageSize: 50,
    compressionEnabled: true,
  },
  advanced: {
    debugMode: false,
    performanceMode: false,
    experimentalFeatures: false,
  },
};

/**
 * Settings tabs configuration
 */
const SETTINGS_TABS = [
  {
    id: "appearance",
    label: "Appearance",
    icon: Palette,
  },
  {
    id: "data",
    label: "Data",
    icon: Database,
  },
  {
    id: "advanced",
    label: "Advanced",
    icon: Zap,
  },
];

type SettingsTab = (typeof SETTINGS_TABS)[number]["id"];

// ============================================================================
// SETTINGS PAGE COMPONENT
// ============================================================================

/**
 * Main settings page component
 */
export default function SettingsPage() {
  const [activeTab, setActiveTab] = React.useState<SettingsTab>("appearance");

  // Settings state with localStorage persistence
  const {
    value: settings,
    setValue: setSettings,
    loading: settingsLoading,
  } = useLocalStorage<AppSettingsType>("app-settings", {
    defaultValue: DEFAULT_SETTINGS,
    syncAcrossTabs: true,
  });

  /**
   * Update a specific settings section
   */
  const updateSettings = React.useCallback(
    (section: keyof AppSettingsType, updates: Partial<AppSettingsType[keyof AppSettingsType]>) => {
      if (!settings) return;

      const newSettings = {
        ...settings,
        [section]: {
          ...settings[section],
          ...updates,
        },
      };

      // Validate settings before saving
      const validation = validateAppSettings(newSettings);
      if (validation.isValid) {
        setSettings(newSettings);
      } else {
        console.error("Settings validation failed:", validation.errors);
      }
    },
    [settings, setSettings]
  );

  /**
   * Reset settings to defaults
   */
  const resetSettings = React.useCallback(() => {
    setSettings(DEFAULT_SETTINGS);
  }, [setSettings]);

  /**
   * Loading component for lazy-loaded settings
   */
  const SettingsLoading = () => (
    <div className="flex items-center justify-center py-8">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  );

  // Show loading state while settings are loading
  if (settingsLoading || !settings) {
    return (
      <Layout>
        <PageContainer title="Settings" description="Customize your disc golf inventory experience">
          <SettingsLoading />
        </PageContainer>
      </Layout>
    );
  }

  return (
    <Layout>
      <PageContainer title="Settings" description="Customize your disc golf inventory experience">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Settings Navigation */}
          <Tabs
            tabs={SETTINGS_TABS}
            activeTab={activeTab}
            onTabChange={(tabId) => setActiveTab(tabId as SettingsTab)}
            variant="underline"
            size="md"
          />

          {/* Settings Content */}
          <div className="min-h-[400px]">
            <React.Suspense fallback={<SettingsLoading />}>
              <TabContent tabId="appearance" activeTab={activeTab}>
                <AppearanceSettings
                  settings={settings.appearance}
                  onUpdate={(updates) => updateSettings("appearance", updates)}
                />
              </TabContent>

              <TabContent tabId="data" activeTab={activeTab}>
                <DataSettings
                  settings={settings.data}
                  onUpdate={(updates) => updateSettings("data", updates)}
                  onReset={resetSettings}
                />
              </TabContent>

              <TabContent tabId="advanced" activeTab={activeTab}>
                <AdvancedSettings
                  settings={settings.advanced}
                  onUpdate={(updates) => updateSettings("advanced", updates)}
                  onReset={resetSettings}
                />
              </TabContent>
            </React.Suspense>
          </div>

          {/* Settings Footer */}
          <div className="border-t pt-6">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <Settings className="h-4 w-4" />
                <span>Settings are automatically saved</span>
              </div>
              <div>Version 1.0.0</div>
            </div>
          </div>
        </div>
      </PageContainer>
    </Layout>
  );
}
