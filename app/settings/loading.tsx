/**
 * Loading Component for Settings Page
 * 
 * Provides a loading state while the settings page is being loaded.
 */

import { Layout, PageContainer } from "@/components/layout/Layout";
import { Skeleton } from "@/components/ui/skeleton";

export default function SettingsLoading() {
  return (
    <Layout>
      <PageContainer
        title="Settings"
        description="Customize your disc golf inventory experience"
      >
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Tabs skeleton */}
          <div className="flex space-x-1 border-b border-border">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-10 w-24" />
            ))}
          </div>

          {/* Content skeleton */}
          <div className="space-y-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="space-y-4">
                <Skeleton className="h-6 w-48" />
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-48" />
                    </div>
                    <Skeleton className="h-6 w-12" />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-40" />
                      <Skeleton className="h-3 w-56" />
                    </div>
                    <Skeleton className="h-6 w-12" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </PageContainer>
    </Layout>
  );
}
